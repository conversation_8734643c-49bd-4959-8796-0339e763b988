const mongoose = require('mongoose'),
{ ShipRocketStatusCodes } = require('../enums'),
    Schema = mongoose.Schema;

const OrderSchema = new Schema({
    user: {
        type: Schema.Types.ObjectId,
        required: true,
        ref: 'User',
    },
    orderId: {
        type: String,
        default: '',
    },
    shiprocket_order_id: {
        type: String,
        default: '',
    },
    products: [
        {
            name: {
                type: String,
                trim: true,
            },
            sku: {
                type: String,
                trim: true,
            },
            productId: {
                type: Schema.Types.ObjectId,
                required: true,
                ref: 'Product',
            },
            mainImage: {
                type: String,
                default: '',
            },
            quantity: {
                type: Number,
                required: true,
                min: 1,
            },
            selectedSize: {
                type: String,
                required: false,
            },
            selectedColor: {
                name: String,
                hexCode: String,
            },
            price: {
                type: Number,
                required: true,
            },
            total: {
                type: Number,
                required: true,
            },
        },
    ],
    status: {
        type: String,
        enum: ShipRocketStatusCodes,
        default: 0,
    },
    paymentMethod: {
        type: String,
        enum: ['Credit Card', 'Debit Card', 'UPI', 'Cash on Delivery'],
        required: true,
    },
    shippingAddress: {
        addressLine1: {
            type: String,
            required: true,
        },
        addressLine2: {
            type: String,
            // required: false,
        },
        city: {
            type: String,
            required: true,
        },
        state: {
            type: String,
            required: true,
        },
        postalCode: {
            type: String,
            required: true,
        },
        country: {
            type: String,
            required: true,
        },
        phone: {
            type: String,
            required: true,
        },
        email: {
            type: String,
            required: true,
        },
        fullName: {
            type: String,
            // required: true,
        },
        lName: {
            type: String,
            // required: true,
        },
        cName: {
            type: String,
        },
    },

    coupoun: {
        code: {
            type: String,
            default: '',
        },
        cDiscount: {
            type: Number,
            default: 0,
        },
    },
    totalPrice: {
        type: Number,
        required: true,
    },
    totalShipping: {
        type: Number,
        default:0
    },
    totalDiscount: {
        type: Number,
        required: true,
        default: 0,
    },
    gTotalPrice: {
        type: Number,
        required: true,
        default: 0,
    },
    paymentInfo: {
        razorpay_payment_id: {
            type: String,
        },
        razorpay_order_id: {
            type: String,
        },
        razorpay_signature: {
            type: String,
        },
    },
    shipments: [{
        type: Schema.Types.ObjectId,
        ref: 'Shipment',
    }],
    refund_shipment: [{
        type: Schema.Types.ObjectId,
        ref: 'Shipment',
    }],
    paidAt: {
        type: Date,
        default: Date.now,
    },
    waybill: {
        type: String,
        default: null,
    },
    deliveredAt: {
        type: Date,
    },
    createdAt: {
        type: Date,
        default: Date.now,
    },
});

module.exports = mongoose.model('Order', OrderSchema);
