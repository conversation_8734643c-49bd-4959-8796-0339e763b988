import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  useTheme,
  useMediaQuery,
  CircularProgress,
} from "@mui/material";
import { useNavigate, useParams } from "react-router-dom";
import {
  useGetOrderInfoQuery,
  useRefundOrderMutation,
  useTrackOrderQuery,
  useUpdateStatusMutation,
} from "services";
import { useEffect, useMemo, useState } from "react";
import { toast } from "react-toastify";
import { useDispatch } from "react-redux";
import { setConfirmModalConfig } from "store/slices/utilSlice";
import parse from "html-react-parser";
import { GLOBAL_SETTINGS } from "constants/constantData";
import { formatTimestamp } from "utils/common";
import {
  Download,
  LocalShipping,
  Payment,
  Schedule,
  Receipt,
  Event,
  Phone,
  Email,
  EventNote,
  CreditCard,
} from "@mui/icons-material";
import dataGridStyles from "../../styles/dataGridStyles";
import { useAcceptOrder } from "hooks";

const View = () => {
  const theme = useTheme();
  const isNonMobile = useMediaQuery(theme.breakpoints.up("sm"));
  const { id } = useParams();
  const [orderStatus, setOrderStatus] = useState("");
  const [isCancelling, setIsCancelling] = useState(false);
  const [isRefunding, setIsRefunding] = useState(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const styles = dataGridStyles(theme.palette.mode);

  const { data: orderData, refetch } = useGetOrderInfoQuery(id, {
    skip: !id,
    refetchOnMountOrArgChange: true,
  });

  const [updateStatus] = useUpdateStatusMutation();
  const [initiateRefund] = useRefundOrderMutation();

  const { onClickAccept } = useAcceptOrder({ refetch });

  useEffect(() => {
    if (orderData?.status) {
      setOrderStatus(orderData.status);
    }
  }, [orderData?.status]);

  const shipmentInfo = orderData?.shipments?.length
    ? orderData?.shipments[0]
    : {};

  const handleCancelOrder = async (orderId) => {
    setIsCancelling(true);
    try {
      const result = await updateStatus({
        orderId,
        status: orderData?.status == "68" ? "2" : "45",
      });
      if (result?.data?._id) {
        if (orderData?.paymentMethod !== "Cash on Delivery") {
          const refundResult = await initiateRefund({
            orderId
          });
          if (refundResult?.data?.id) {
            refetch();
            toast.success("Order cancelled successfully!");
          }
        } else {
          refetch();
          toast.success("Order cancelled successfully!");
        }
      }
    } catch (err) {
      console.log(err);
      toast.error("Failed to cancel order. Please try again.");
    } finally {
      setIsCancelling(false);
    }
  };

  const onClickRefund = async (orderId) => {
    setIsRefunding(true);
    try {
      const result = await initiateRefund({
        orderId,
      });
      if (result?.success) {
        refetch();
        toast.success("Refund initiated successfully!");
      }
    } catch (err) {
      console.log(err);
      toast.error("Failed to initiate refund. Please try again.");
    } finally {
      setIsRefunding(false);
    }
  };


  const awb_code = useMemo(() => {
    return orderData?.shipments?.[0]?.awb_code;
  }, [orderData]);

  // const [shipmentStatus, setShipmentStatus] = useState(null);

  const { data: { tracking_data } = {} } = useTrackOrderQuery(awb_code, {
    skip: !awb_code,
  });

  useEffect(() => {
    if (tracking_data) {
      updateStatus({
        orderId: orderData?._id,
        status: tracking_data?.shipment_status?.toString(),
      }).then(() => {
        refetch();
      }).catch(err => {
        console.log(err);
      })
    }
  }, [tracking_data]);

  return (
    <Box m={2}>
      {/* Header Section */}
      <Box display="flex" justifyContent="space-between" mb={3}>
        <Typography variant="h4" fontWeight="bold">
          Order Details
        </Typography>
        <Box display="flex" gap={2}>
          {["9"]?.includes(orderData?.status) &&
            orderData?.paymentMethod !== "Cash on Delivery" && (
              <Button
                variant="contained"
                color="success"
                onClick={() => onClickRefund(orderData?._id)}
                disabled={isRefunding}
                startIcon={isRefunding ? <CircularProgress size={16} color="inherit" /> : null}
              >
                {isRefunding ? "Processing Refund..." : "Initiate Refund"}
              </Button>
            )}
          {["68"]?.includes(orderData?.status) && (
            <Button
              variant="contained"
              color="success"
              onClick={() => onClickAccept(orderData?._id)}
            >
              Accept Order
            </Button>
          )}
          {!["7", "2", "45", "9", "8"]?.includes(orderData?.status) && (
            <Button
              variant="contained"
              color="error"
              disabled={isCancelling}
              startIcon={isCancelling ? <CircularProgress size={16} color="inherit" /> : null}
              onClick={() => {
                if (!isCancelling) {
                  dispatch(
                    setConfirmModalConfig({
                      visible: true,
                      data: {
                        onSubmit: () => handleCancelOrder(orderData?._id),
                        content: {
                          heading: "Cancel Order",
                          description:
                            "Are you sure you want to cancel this order?",
                        },
                      },
                    })
                  );
                }
              }}
            >
              {isCancelling ? "Cancelling..." : "Cancel Order"}
            </Button>
          )}
          <Button
            sx={styles.buttonMd}
            variant="contained"
            color="secondary"
            onClick={() => navigate("/orders")}
          >
            Back to Orders
          </Button>
        </Box>
      </Box>
      {/* Order Info Section */}
      <Grid container spacing={3}>
        {/* Order Info Card */}
        <Grid item xs={12} lg={4}>
          <Card
            sx={{
              backgroundColor: "#f5f5f5",
              borderRadius: 2,
              boxShadow: 3,
              height: "100%",
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Typography
                variant="h6"
                fontWeight="bold"
                align="center"
                color="primary"
                gutterBottom
                sx={{ mb: 3 }}
              >
                Order Information
              </Typography>

              <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <CreditCard sx={{ fontSize: 20, color: "gray" }} />
                  <Typography variant="body2" sx={{ fontWeight: 600, color: "gray", minWidth: "120px" }}>
                    Order ID:
                  </Typography>
                  <Typography variant="body2" sx={{ wordBreak: "break-all" }}>
                    {orderData?.orderId}
                  </Typography>
                </Box>

                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <EventNote sx={{ fontSize: 20, color: "gray" }} />
                  <Typography variant="body2" sx={{ fontWeight: 600, color: "gray", minWidth: "120px" }}>
                    Status:
                  </Typography>
                  <Typography variant="body2" sx={{ color: "#ff9800", fontWeight: 600 }}>
                    {orderData?.statusText}
                  </Typography>
                </Box>

                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Payment sx={{ fontSize: 20, color: "gray" }} />
                  <Typography variant="body2" sx={{ fontWeight: 600, color: "gray", minWidth: "120px" }}>
                    Total Price:
                  </Typography>
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                    {parse(GLOBAL_SETTINGS.CURRENCY)} {orderData?.totalPrice}
                  </Typography>
                </Box>

                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Payment sx={{ fontSize: 20, color: "gray" }} />
                  <Typography variant="body2" sx={{ fontWeight: 600, color: "gray", minWidth: "120px" }}>
                    Payment Method:
                  </Typography>
                  <Typography variant="body2">
                    {orderData?.paymentMethod}
                  </Typography>
                </Box>

                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Payment sx={{ fontSize: 20, color: "gray" }} />
                  <Typography variant="body2" sx={{ fontWeight: 600, color: "gray", minWidth: "120px" }}>
                    Transaction ID:
                  </Typography>
                  <Typography variant="body2" sx={{ wordBreak: "break-all" }}>
                    {orderData?.paymentInfo?.razorpay_payment_id || "N/A"}
                  </Typography>
                </Box>

                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <EventNote sx={{ fontSize: 20, color: "gray" }} />
                  <Typography variant="body2" sx={{ fontWeight: 600, color: "gray", minWidth: "120px" }}>
                    Paid At:
                  </Typography>
                  <Typography variant="body2">
                    {formatTimestamp(orderData?.paidAt)}
                  </Typography>
                </Box>

                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <EventNote sx={{ fontSize: 20, color: "gray" }} />
                  <Typography variant="body2" sx={{ fontWeight: 600, color: "gray", minWidth: "120px" }}>
                    Created At:
                  </Typography>
                  <Typography variant="body2">
                    {formatTimestamp(orderData?.createdAt)}
                  </Typography>
                </Box>

                {orderData?.coupoun?.code && (
                  <>
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <EventNote sx={{ fontSize: 20, color: "gray" }} />
                      <Typography variant="body2" sx={{ fontWeight: 600, color: "gray", minWidth: "120px" }}>
                        Coupon Code:
                      </Typography>
                      <Typography variant="body2" sx={{ color: "green", fontWeight: 600 }}>
                        {orderData?.coupoun?.code}
                      </Typography>
                    </Box>

                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <EventNote sx={{ fontSize: 20, color: "gray" }} />
                      <Typography variant="body2" sx={{ fontWeight: 600, color: "gray", minWidth: "120px" }}>
                        Coupon Discount:
                      </Typography>
                      <Typography variant="body2" sx={{ color: "green", fontWeight: 600 }}>
                        ₹{orderData?.coupoun?.cDiscount || 0}
                      </Typography>
                    </Box>
                  </>
                )}

                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <LocalShipping sx={{ fontSize: 20, color: "gray" }} />
                  <Typography variant="body2" sx={{ fontWeight: 600, color: "gray", minWidth: "120px" }}>
                    Shipping:
                  </Typography>
                  <Typography variant="body2">
                    ₹{orderData?.totalShipping || 0}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Shipping Address Card */}
        <Grid item xs={12} lg={4}>
          <Card
            sx={{
              backgroundColor: "#f5f5f5",
              borderRadius: 2,
              boxShadow: 3,
              height: "100%",
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Typography
                variant="h6"
                fontWeight="bold"
                align="center"
                color="primary"
                gutterBottom
                sx={{ mb: 3 }}
              >
                Shipping Address
              </Typography>

              <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <LocalShipping sx={{ fontSize: 20, color: "gray" }} />
                  <Typography variant="body2" sx={{ fontWeight: 600, color: "gray", minWidth: "100px" }}>
                    Name:
                  </Typography>
                  <Typography variant="body2">
                    {orderData?.shippingAddress?.fullName}
                  </Typography>
                </Box>

                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Phone sx={{ fontSize: 20, color: "gray" }} />
                  <Typography variant="body2" sx={{ fontWeight: 600, color: "gray", minWidth: "100px" }}>
                    Phone:
                  </Typography>
                  <Typography variant="body2">
                    {orderData?.shippingAddress?.phone}
                  </Typography>
                </Box>

                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Email sx={{ fontSize: 20, color: "gray" }} />
                  <Typography variant="body2" sx={{ fontWeight: 600, color: "gray", minWidth: "100px" }}>
                    Email:
                  </Typography>
                  <Typography variant="body2" sx={{ wordBreak: "break-all" }}>
                    {orderData?.shippingAddress?.email}
                  </Typography>
                </Box>

                <Box sx={{ display: "flex", alignItems: "flex-start", gap: 1 }}>
                  <LocalShipping sx={{ fontSize: 20, color: "gray", mt: 0.5 }} />
                  <Typography variant="body2" sx={{ fontWeight: 600, color: "gray", minWidth: "100px" }}>
                    Address:
                  </Typography>
                  <Typography variant="body2" sx={{ lineHeight: 1.5 }}>
                    {orderData?.shippingAddress?.addressLine1}
                    {orderData?.shippingAddress?.addressLine2 && `, ${orderData?.shippingAddress?.addressLine2}`}
                    <br />
                    {orderData?.shippingAddress?.city}, {orderData?.shippingAddress?.state}
                    <br />
                    {orderData?.shippingAddress?.country} - {orderData?.shippingAddress?.postalCode}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Refund Details Card - Only show if refund data exists */}
        {orderData?.paymentInfo?.refund && (
          <Grid item xs={12} lg={4}>
            <Card
              sx={{
                backgroundColor: "#f5f5f5",
                borderRadius: 2,
                boxShadow: 3,
                height: "100%",
                border: "2px solid #4caf50",
              }}
            >
              <CardContent sx={{ p: 3 }}>
                <Typography
                  variant="h6"
                  fontWeight="bold"
                  align="center"
                  color="success.main"
                  gutterBottom
                  sx={{ mb: 3 }}
                >
                  Refund Details
                </Typography>

                <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                  <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                    <Receipt sx={{ fontSize: 20, color: "green" }} />
                    <Typography variant="body2" sx={{ fontWeight: 600, color: "gray", minWidth: "120px" }}>
                      Refund ID:
                    </Typography>
                    <Typography variant="body2" sx={{ wordBreak: "break-all", color: "green", fontWeight: 600 }}>
                      {orderData?.paymentInfo?.refund?.id}
                    </Typography>
                  </Box>

                  <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                    <Payment sx={{ fontSize: 20, color: "green" }} />
                    <Typography variant="body2" sx={{ fontWeight: 600, color: "gray", minWidth: "120px" }}>
                      Refund Amount:
                    </Typography>
                    <Typography variant="body2" sx={{ color: "green", fontWeight: 600 }}>
                      ₹{(orderData?.paymentInfo?.refund?.amount / 100).toFixed(2)}
                    </Typography>
                  </Box>

                  <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                    <EventNote sx={{ fontSize: 20, color: "green" }} />
                    <Typography variant="body2" sx={{ fontWeight: 600, color: "gray", minWidth: "120px" }}>
                      Status:
                    </Typography>
                    <Typography variant="body2" sx={{
                      color: orderData?.paymentInfo?.refund?.status === 'processed' ? 'green' : 'orange',
                      fontWeight: 600,
                      textTransform: 'capitalize'
                    }}>
                      {orderData?.paymentInfo?.refund?.status}
                    </Typography>
                  </Box>

                  <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                    <Schedule sx={{ fontSize: 20, color: "green" }} />
                    <Typography variant="body2" sx={{ fontWeight: 600, color: "gray", minWidth: "120px" }}>
                      Processed At:
                    </Typography>
                    <Typography variant="body2">
                      {orderData?.paymentInfo?.refund?.created_at
                        ? new Date(orderData?.paymentInfo?.refund?.created_at * 1000).toLocaleString()
                        : 'N/A'
                      }
                    </Typography>
                  </Box>

                  {orderData?.paymentInfo?.refund?.speed_processed && (
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <EventNote sx={{ fontSize: 20, color: "green" }} />
                      <Typography variant="body2" sx={{ fontWeight: 600, color: "gray", minWidth: "120px" }}>
                        Speed:
                      </Typography>
                      <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                        {orderData?.paymentInfo?.refund?.speed_processed}
                      </Typography>
                    </Box>
                  )}

                  {orderData?.paymentInfo?.refund?.receipt && (
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <Receipt sx={{ fontSize: 20, color: "green" }} />
                      <Typography variant="body2" sx={{ fontWeight: 600, color: "gray", minWidth: "120px" }}>
                        Receipt:
                      </Typography>
                      <Typography variant="body2" sx={{ wordBreak: "break-all" }}>
                        {orderData?.paymentInfo?.refund?.receipt}
                      </Typography>
                    </Box>
                  )}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>

      {/* Shipment Info Section */}
      <Grid container spacing={3} mt={2}>
        <Grid item xs={12}>
          <Card
            sx={{
              backgroundColor: "#f9f9f9",
              borderRadius: 2,
              boxShadow: 3,
              border: shipmentInfo?.awb_code ? "1px solid #2196f3" : "1px solid #e0e0e0"
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                <Typography
                  variant="h6"
                  fontWeight="bold"
                  color="primary"
                  sx={{ display: "flex", alignItems: "center", gap: 1 }}
                >
                  <LocalShipping />
                  Shipment Information
                </Typography>

                {/* Action Buttons */}
                <Box display="flex" gap={2}>
                  {shipmentInfo?.label_url && (
                    <Button
                      variant="contained"
                      color="primary"
                      size="small"
                      startIcon={<Download />}
                      href={shipmentInfo?.label_url}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Download Label
                    </Button>
                  )}
                  {shipmentInfo?.manifest_url && (
                    <Button
                      variant="contained"
                      color="secondary"
                      size="small"
                      startIcon={<Download />}
                      href={shipmentInfo?.manifest_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      sx={styles.buttonMd}
                    >
                      Download Manifest
                    </Button>
                  )}
                </Box>
              </Box>

              {/* Shipment Details Grid */}
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <LocalShipping sx={{ fontSize: 20, color: "gray" }} />
                      <Typography variant="body2" sx={{ fontWeight: 600, color: "gray", minWidth: "140px" }}>
                        Courier Name:
                      </Typography>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {shipmentInfo?.courier_name || "N/A"}
                      </Typography>
                    </Box>

                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <Receipt sx={{ fontSize: 20, color: "gray" }} />
                      <Typography variant="body2" sx={{ fontWeight: 600, color: "gray", minWidth: "140px" }}>
                        AWB Code:
                      </Typography>
                      <Typography variant="body2" sx={{
                        color: shipmentInfo?.awb_code ? "#ff9800" : "text.secondary",
                        fontWeight: shipmentInfo?.awb_code ? 600 : 400,
                        wordBreak: "break-all"
                      }}>
                        {shipmentInfo?.awb_code || "Not Generated"}
                      </Typography>
                    </Box>

                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <Event sx={{ fontSize: 20, color: "gray" }} />
                      <Typography variant="body2" sx={{ fontWeight: 600, color: "gray", minWidth: "140px" }}>
                        Shipping Status:
                      </Typography>
                      <Typography variant="body2" sx={{
                        color: shipmentInfo?.shipping_status ? "primary.main" : "text.secondary",
                        fontWeight: 500
                      }}>
                        {shipmentInfo?.shipping_status || "N/A"}
                      </Typography>
                    </Box>

                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <Payment sx={{ fontSize: 20, color: "gray" }} />
                      <Typography variant="body2" sx={{ fontWeight: 600, color: "gray", minWidth: "140px" }}>
                        Payment Type:
                      </Typography>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {orderData?.paymentMethod === "Cash on Delivery" ? "Postpaid (COD)" : "Prepaid"}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <Receipt sx={{ fontSize: 20, color: "gray" }} />
                      <Typography variant="body2" sx={{ fontWeight: 600, color: "gray", minWidth: "140px" }}>
                        Shipment ID:
                      </Typography>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {shipmentInfo?.shipment_id || "N/A"}
                      </Typography>
                    </Box>

                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <Schedule sx={{ fontSize: 20, color: "gray" }} />
                      <Typography variant="body2" sx={{ fontWeight: 600, color: "gray", minWidth: "140px" }}>
                        Pickup Scheduled:
                      </Typography>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {shipmentInfo?.pickup_scheduled_date
                          ? new Date(shipmentInfo.pickup_scheduled_date).toLocaleDateString()
                          : "N/A"
                        }
                      </Typography>
                    </Box>

                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <Event sx={{ fontSize: 20, color: "gray" }} />
                      <Typography variant="body2" sx={{ fontWeight: 600, color: "gray", minWidth: "140px" }}>
                        Pickup Token:
                      </Typography>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {shipmentInfo?.pickup_token_number || "N/A"}
                      </Typography>
                    </Box>

                    {shipmentInfo?.freight_charges && (
                      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                        <Payment sx={{ fontSize: 20, color: "gray" }} />
                        <Typography variant="body2" sx={{ fontWeight: 600, color: "gray", minWidth: "140px" }}>
                          Freight Charges:
                        </Typography>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          ₹{shipmentInfo?.freight_charges}
                        </Typography>
                      </Box>
                    )}
                  </Box>
                </Grid>
              </Grid>

              {/* Status Indicator */}
              {!shipmentInfo?.awb_code && (
                <Box
                  sx={{
                    mt: 3,
                    p: 2,
                    backgroundColor: "#fff3cd",
                    borderRadius: 1,
                    border: "1px solid #ffeaa7"
                  }}
                >
                  <Typography variant="body2" sx={{ color: "#856404", textAlign: "center" }}>
                    ⚠️ Shipment not yet created. AWB code will be generated once the shipment is processed.
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      {/* Products Section */}
      <Grid container spacing={3} mt={2}>
        <Grid item xs={12}>
          <Card sx={{ backgroundColor: "#f9f9f9", borderRadius: 2, boxShadow: 3 }}>
            <CardContent sx={{ p: 3 }}>
              <Typography
                variant="h6"
                fontWeight="bold"
                color="primary"
                sx={{ mb: 3, display: "flex", alignItems: "center", gap: 1 }}
              >
                <Receipt />
                Ordered Products ({orderData?.products?.length || 0})
              </Typography>

              <Grid container spacing={3}>
                {orderData?.products?.map((item, index) => (
                  <Grid item xs={12} sm={6} lg={4} key={index}>
                    <Card
                      sx={{
                        backgroundColor: "#ffffff",
                        borderRadius: 2,
                        boxShadow: 2,
                        height: "100%",
                        display: "flex",
                        flexDirection: "column",
                        transition: "transform 0.2s ease-in-out",
                        "&:hover": {
                          transform: "translateY(-2px)",
                          boxShadow: 4,
                        },
                      }}
                    >
                      <Box
                        sx={{
                          width: "100%",
                          height: "200px",
                          backgroundImage: `url(${
                            item?.mainImage ?? item?.productId?.mainImage
                          })`,
                          backgroundSize: "cover",
                          backgroundRepeat: "no-repeat",
                          backgroundPosition: "center",
                          borderRadius: "8px 8px 0 0",
                          position: "relative",
                        }}
                      >
                        {/* Quantity Badge */}
                        <Box
                          sx={{
                            position: "absolute",
                            top: 8,
                            right: 8,
                            backgroundColor: "primary.main",
                            color: "white",
                            borderRadius: "50%",
                            width: 32,
                            height: 32,
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            fontSize: "0.875rem",
                            fontWeight: 600,
                          }}
                        >
                          {item?.quantity}
                        </Box>
                      </Box>

                      <CardContent sx={{ p: 2, flexGrow: 1 }}>
                        <Typography
                          variant="h6"
                          fontWeight="bold"
                          sx={{
                            mb: 2,
                            color: "text.primary",
                            fontSize: "1rem",
                            lineHeight: 1.3,
                            display: "-webkit-box",
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: "vertical",
                            overflow: "hidden",
                          }}
                        >
                          {item?.name || item?.productId?.name}
                        </Typography>

                        <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
                          <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                            <Typography variant="body2" sx={{ fontWeight: 600, color: "gray" }}>
                              Price:
                            </Typography>
                            <Typography variant="body2" sx={{ fontWeight: 600, color: "primary.main" }}>
                              {parse(GLOBAL_SETTINGS.CURRENCY)}{item?.price}
                            </Typography>
                          </Box>

                          <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                            <Typography variant="body2" sx={{ fontWeight: 600, color: "gray" }}>
                              Total:
                            </Typography>
                            <Typography variant="body2" sx={{ fontWeight: 600, color: "success.main" }}>
                              {parse(GLOBAL_SETTINGS.CURRENCY)}{item?.total || (item?.price * item?.quantity)}
                            </Typography>
                          </Box>

                          {item?.selectedSize && (
                            <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                              <Typography variant="body2" sx={{ fontWeight: 600, color: "gray" }}>
                                Size:
                              </Typography>
                              <Typography variant="body2" sx={{
                                backgroundColor: "grey.100",
                                px: 1,
                                py: 0.5,
                                borderRadius: 1,
                                fontSize: "0.75rem",
                                fontWeight: 600
                              }}>
                                {item?.selectedSize}
                              </Typography>
                            </Box>
                          )}

                          {item?.selectedColor?.name && (
                            <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                              <Typography variant="body2" sx={{ fontWeight: 600, color: "gray" }}>
                                Color:
                              </Typography>
                              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                                {item?.selectedColor?.hexCode && (
                                  <Box
                                    sx={{
                                      width: 16,
                                      height: 16,
                                      borderRadius: "50%",
                                      backgroundColor: item?.selectedColor?.hexCode,
                                      border: "1px solid #ddd",
                                    }}
                                  />
                                )}
                                <Typography variant="body2" sx={{
                                  fontSize: "0.75rem",
                                  fontWeight: 600,
                                  textTransform: "capitalize"
                                }}>
                                  {item?.selectedColor?.name}
                                </Typography>
                              </Box>
                            </Box>
                          )}

                          {item?.sku && (
                            <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                              <Typography variant="body2" sx={{ fontWeight: 600, color: "gray" }}>
                                SKU:
                              </Typography>
                              <Typography variant="body2" sx={{
                                fontSize: "0.75rem",
                                color: "text.secondary",
                                fontFamily: "monospace"
                              }}>
                                {item?.sku}
                              </Typography>
                            </Box>
                          )}
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default View;
