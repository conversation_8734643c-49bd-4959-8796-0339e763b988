const PDFDocument = require('pdfkit');
const fs = require('fs');
const path = require('path');
const { formatDate } = require('../util');

/**
 * Generates a PDF invoice for an order
 * @param {Object} order - The order object
 * @param {Object} user - The user object
 * @param {String} outputPath - The path where the PDF should be saved
 * @returns {Promise<String>} - The path to the generated PDF
 */
const generateInvoice = async (order, user, outputPath = null) => {
    return new Promise((resolve, reject) => {
        try {
            // Create a new PDF document
            const doc = new PDFDocument({ margin: 50 });

            // If outputPath is not provided, generate a temporary file path
            if (!outputPath) {
                const tempDir = path.join(__dirname, '../../temp');

                // Create temp directory if it doesn't exist
                if (!fs.existsSync(tempDir)) {
                    fs.mkdirSync(tempDir, { recursive: true });
                }

                outputPath = path.join(tempDir, `invoice-${order.orderId}.pdf`);
            }

            // Pipe the PDF to a file
            const stream = fs.createWriteStream(outputPath);
            doc.pipe(stream);

            // Add company logo (fixed size, no shrinking)
            const logoPath = path.join(__dirname, '../../../kurti-frontend/public/logo.png');
            if (fs.existsSync(logoPath)) {
                doc.image(logoPath, 50, 50, {
                    width: 120,
                    height: 80,
                    fit: [120, 80],
                    align: 'center',
                    valign: 'center',
                });
            }

            // Add company information (positioned to not overlap with logo)
            doc.fontSize(24)
                .fillColor('#000')
                .text('Gajan Creation', 50, 140);

            // Add invoice header on the right (with proper spacing to avoid overlap)
            doc.fontSize(18)
                .fillColor('#000')
                .text('Invoice', 400, 50, { align: 'right', width: 150 });

            doc.fontSize(11)
                .fillColor('#666')
                .text(`Invoice #: ${order.orderId || 'N/A'}`, 400, 80, { align: 'right', width: 150 });

            doc.fontSize(11)
                .fillColor('#666')
                .text(`Date: ${formatDate(order.createdAt) || 'N/A'}`, 400, 100, { align: 'right', width: 150 });

            // Add a line separator (moved down to avoid overlap)
            doc.moveTo(50, 180)
                .lineTo(550, 180)
                .stroke();

            // Add billing information (with proper spacing)
            const shippingAddr = order.shippingAddress || {};
            const billingStartY = 200;

            doc.fontSize(12)
                .fillColor('#000')
                .text('Billing Information:', 50, billingStartY);
            doc.fontSize(10)
                .fillColor('#333')
                .text(`Name: ${shippingAddr.fullName || 'N/A'}`, 50, billingStartY + 20);
            doc.fontSize(10)
                .fillColor('#333')
                .text(`Address: ${shippingAddr.addressLine1 || 'N/A'}`, 50, billingStartY + 35);

            let currentY = billingStartY + 50;
            if (shippingAddr.addressLine2) {
                doc.fontSize(10)
                    .fillColor('#333')
                    .text(`${shippingAddr.addressLine2}`, 50, currentY);
                currentY += 15;
            }

            doc.fontSize(10)
                .fillColor('#333')
                .text(
                    `${shippingAddr.city || 'N/A'}, ${shippingAddr.state || 'N/A'} ${shippingAddr.pincode || 'N/A'}`,
                    50,
                    currentY
                );
            doc.fontSize(10)
                .fillColor('#333')
                .text(`Phone: ${shippingAddr.phone || 'N/A'}`, 50, currentY + 15);
            doc.fontSize(10)
                .fillColor('#333')
                .text(`Email: ${shippingAddr.email || 'N/A'}`, 50, currentY + 30);

            // Add shipping information (right column)
            doc.fontSize(12)
                .fillColor('#000')
                .text('Shipping Information:', 320, billingStartY);
            doc.fontSize(10)
                .fillColor('#333')
                .text(`Name: ${shippingAddr.fullName || 'N/A'}`, 320, billingStartY + 20);
            doc.fontSize(10)
                .fillColor('#333')
                .text(`Address: ${shippingAddr.addressLine1 || 'N/A'}`, 320, billingStartY + 35);

            let shippingY = billingStartY + 50;
            if (shippingAddr.addressLine2) {
                doc.fontSize(10)
                    .fillColor('#333')
                    .text(`${shippingAddr.addressLine2}`, 320, shippingY);
                shippingY += 15;
            }

            doc.fontSize(10)
                .fillColor('#333')
                .text(
                    `${shippingAddr.city || 'N/A'}, ${shippingAddr.state || 'N/A'} ${shippingAddr.pincode || 'N/A'}`,
                    320,
                    shippingY
                );
            doc.fontSize(10)
                .fillColor('#333')
                .text(`Phone: ${shippingAddr.phone || 'N/A'}`, 320, shippingY + 15);

            // Calculate dynamic Y position based on address content (ensure no overlap)
            const maxAddressY = Math.max(currentY + 45, shippingY + 30);
            const tableStartY = maxAddressY + 20;

            // Add a line before table
            doc.moveTo(50, tableStartY)
                .lineTo(550, tableStartY)
                .stroke();

            // Add table headers with better spacing
            const headerY = tableStartY + 20;
            doc.fontSize(12)
                .fillColor('#000')
                .text('Item', 50, headerY);
            doc.fontSize(12)
                .fillColor('#000')
                .text('Quantity', 300, headerY, { width: 70, align: 'center' });
            doc.fontSize(12)
                .fillColor('#000')
                .text('Price', 390, headerY, { width: 70, align: 'center' });
            doc.fontSize(12)
                .fillColor('#000')
                .text('Total', 480, headerY, { width: 70, align: 'center' });

            // Add a line after headers
            doc.moveTo(50, headerY + 25)
                .lineTo(550, headerY + 25)
                .stroke();

            // Add items with proper spacing
            let y = headerY + 45;
            const products = order.products || [];
            products.forEach(item => {
                const itemPrice = parseFloat(item.price) || 0;
                const itemQuantity = parseInt(item.quantity) || 1;
                const itemTotal = itemPrice * itemQuantity;

                doc.fontSize(11)
                    .fillColor('#333')
                    .text(item.name || 'Unknown Item', 50, y, { width: 240 });
                doc.fontSize(11)
                    .fillColor('#333')
                    .text(itemQuantity.toString(), 300, y, { width: 70, align: 'center' });
                doc.fontSize(11)
                    .fillColor('#333')
                    .text(`Rs.${itemPrice.toFixed(2)}`, 390, y, { width: 70, align: 'center' });
                doc.fontSize(11)
                    .fillColor('#333')
                    .text(`Rs.${itemTotal.toFixed(2)}`, 480, y, { width: 70, align: 'center' });
                y += 30;
            });

            // Add a line before totals with proper spacing
            doc.moveTo(50, y + 15)
                .lineTo(550, y + 15)
                .stroke();

            // Add totals section with proper spacing
            y += 35;
            const subtotal = parseFloat(order.totalGPrice) || 0;
            const discount = parseFloat(order.discount) || 0;
            const shipping = parseFloat(order.totalShipping) || 0;
            const totalPrice = parseFloat(order.totalPrice) || 0;

            // Subtotal
            doc.fontSize(11)
                .fillColor('#333')
                .text('Subtotal:', 400, y);
            doc.fontSize(11)
                .fillColor('#333')
                .text(`Rs.${subtotal.toFixed(2)}`, 480, y, { width: 70, align: 'right' });

            y += 25;
            if (discount > 0) {
                doc.fontSize(11)
                    .fillColor('#333')
                    .text('Discount:', 400, y);
                doc.fontSize(11)
                    .fillColor('#333')
                    .text(`-Rs.${discount.toFixed(2)}`, 480, y, { width: 70, align: 'right' });
                y += 25;
            }

            if (shipping > 0) {
                doc.fontSize(11)
                    .fillColor('#333')
                    .text('Shipping:', 400, y);
                doc.fontSize(11)
                    .fillColor('#333')
                    .text(`Rs.${shipping.toFixed(2)}`, 480, y, { width: 70, align: 'right' });
                y += 25;
            }

            // Add line before total with proper spacing
            doc.moveTo(400, y + 10)
                .lineTo(550, y + 10)
                .stroke();
            y += 25;

            // Total with larger font
            doc.fontSize(14)
                .fillColor('#000')
                .text('Total:', 400, y);
            doc.fontSize(14)
                .fillColor('#000')
                .text(`Rs.${totalPrice.toFixed(2)}`, 480, y, { width: 70, align: 'right' });

            // Add payment information with proper spacing
            y += 50;
            doc.fontSize(13)
                .fillColor('#000')
                .text('Payment Information:', 50, y);
            y += 25;
            doc.fontSize(11)
                .fillColor('#333')
                .text(`Payment Method: ${order.paymentMethod || 'N/A'}`, 50, y);
            y += 20;

            // Add footer with proper spacing (ensure it doesn't overlap)
            const footerY = Math.max(y + 80, 720);
            doc.fontSize(13)
                .fillColor('#666')
                .text('Thank you for your business!', 50, footerY, {
                    align: 'center',
                    width: 500,
                });

            // Finalize the PDF
            doc.end();

            // When the stream is finished, resolve with the file path
            stream.on('finish', () => {
                resolve(outputPath);
            });

            // If there's an error, reject with the error
            stream.on('error', err => {
                reject(err);
            });
        } catch (error) {
            reject(error);
        }
    });
};

module.exports = { generateInvoice };
