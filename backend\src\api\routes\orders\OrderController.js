const {
    models: { Product, Cart, Order, UserAddress, Coupon, Shipment },
} = require('../../../../lib/models');
const { ShipRocketStatusCodes } = require('../../../../lib/models/enums');
const { getStatusText } = require('../../../../lib/shipping');
const { createShipment, shipmentStatus } = require('../../../../lib/shipping/delhivery');
const {
    generateUniqueOrderId,
    generateShortUniqueOrderId,
    generateShortUniqueOrderIdRandom,
} = require('../../util/common');
const Razorpay = require('razorpay');
const razorpay = new Razorpay({
    key_id: process.env.RAZORPAY_KEY_ID,
    key_secret: process.env.RAZORPAY_KEY_SECRET,
});

const shortid = require('shortid');
const { makeApiRequest } = require('../../util/shipping/shiprocket');

class OrderController {
    async paymentOrder(req, res) {
        const { amount, currency } = req.body;
        const payment_capture = 1;
        const options = {
            amount: amount * 100,
            currency,
            receipt: shortid.generate(),
            payment_capture,
        };

        try {
            const response = await razorpay.orders.create(options);
            return res.success(
                {
                    id: response.id,
                    currency: response.currency,
                    amount: response.amount,
                },
                req.__('CREATE_ORDER')
            );
        } catch (error) {
            console.log('Error while Placing order:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async placeOrder(req, res) {
        const userId = req.user._id;
        const { products, paymentMethod, shippingAddress, paymentInfo, status, totalShipping, couponCode } = req.body;
        try {
            // Validate products
            if (!products || products.length === 0) {
                return res.warn('error', req.__('PLEASE_PROVIDE_PRODUCTS'));
            }

            let totalGPrice = 0;
            let totalPrice = totalShipping;
            let couponDiscount = 0;

            // Loop through each product to validate and calculate prices
            for (const item of products) {
                const product = await Product.findById(item?.productId?._id);
                if (!product) {
                    return res.notFound({}, req.__('PRODUCT_NOT_FOUND'));
                }

                // Find the price based on the selected size
                const sizePrice = product.stock.find(stockItem => stockItem.size === item.selectedSize);
                if (!sizePrice) {
                    return res.notFound({}, req.__('SIZE_NOT_FOUND'));
                }

                const gPrice = sizePrice.unitPrice;
                const price = sizePrice.sellingPrice;

                totalGPrice += gPrice * item.quantity;
                totalPrice += price * item.quantity;

                // Add selected color image to the product item
                const selectedColor = product.attributes.color.find(
                    color => color?.name?.toLowerCase() === item?.selectedColor?.name?.toLowerCase()
                );
                if (selectedColor) {
                    item.mainImage = selectedColor?.images?.mainImage?.url;
                }
                item.name = product.name;
                item.sku = product.sku;
            }

            // Validate and apply coupon if provided
            if (couponCode) {
                const coupon = await Coupon.findOne({ code: couponCode, status: 'Active' });
                if (!coupon) {
                    return res.warn('error', req.__('INVALID_COUPON'));
                }

                if (new Date(coupon.expiry) < new Date()) {
                    return res.warn('error', req.__('COUPON_EXPIRED'));
                }

                if (coupon.minCartValue && totalPrice < coupon.minCartValue) {
                    return res.warn('error', req.__('MIN_CART_VALUE_NOT_MET', { value: coupon.minCartValue }));
                }

                switch (coupon.discountType) {
                    case 'flat':
                        couponDiscount = coupon.discountValue;
                        break;

                    case 'percentage':
                        couponDiscount = (totalPrice * coupon.discountValue) / 100;
                        if (coupon.maxDiscount && couponDiscount > coupon.maxDiscount) {
                            couponDiscount = coupon.maxDiscount;
                        }
                        break;

                    case 'bundle':
                        // Check bundle details (optional, based on your use case)


                        const categoryProducts = products.filter(p => p.productId.categoryId.toString() === coupon.bundleDetails.category.toString());
                        const categoryQuantity = categoryProducts.reduce((sum, p) => sum + p.quantity, 0);


                        if (categoryQuantity >= coupon.bundleDetails.quantity) {
                            couponDiscount = totalPrice - (totalShipping +coupon.bundleDetails.bundlePrice);
                        }
                        break;

                    default:
                        break;
                }

                totalPrice -= couponDiscount;
            }



            const order = new Order({
                user: userId,
                orderId: generateShortUniqueOrderIdRandom(),
                products,
                paymentMethod,
                shippingAddress,
                totalGPrice,
                totalShipping,
                totalPrice,
                discount: totalGPrice - totalPrice,
                paymentInfo,
                status,
                coupoun: {
                    code: couponCode || '',
                    cDiscount: couponDiscount || 0,
                },
            });

            const savedOrder = await order.save();

            // Save or update user address
            const userAddressData = {
                userId: userId,
                addressLine1: shippingAddress.addressLine1,
                addressLine2: shippingAddress.addressLine2,
                city: shippingAddress.city,
                state: shippingAddress.state,
                postalCode: shippingAddress.postalCode,
                country: shippingAddress.country,
                phone: shippingAddress.phone,
                email: shippingAddress.email,
                fName: shippingAddress.fullName,
                nickName: shippingAddress.nickName,
            };

            const existingAddress = await UserAddress.findOne({ userId: userId, nickName: shippingAddress.nickName });

            if (existingAddress) {
                await UserAddress.updateOne(
                    { userId: userId, nickName: shippingAddress.nickName },
                    { $set: userAddressData }
                );
            } else {
                const newAddress = new UserAddress(userAddressData);
                await newAddress.save();
            }

            // Clear the user's cart
            await Cart.findOneAndUpdate({ userId }, { products: [], totalPrice: 0, gTotalPrice: 0, totalDiscount: 0 });

            return res.success(savedOrder, req.__('ORDER_PLACED'));
        } catch (error) {
            console.log('Error while placing order:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async MyOrdersList(req, res) {
        const userId = req.user._id;
        const { page = 1, limit = 10, status, paymentMethod, search } = req.query;

        // Convert page and limit to numbers
        const pageNumber = parseInt(page);
        const limitNumber = parseInt(limit);

        // Calculate the number of items to skip
        let skip = (pageNumber - 1) * limitNumber;

        try {
            let qry = { user: userId };

            if (status) {
                qry.status = status;
            }

            if (paymentMethod) {
                qry.paymentMethod = paymentMethod;
            }

            if (search) {
                qry.$or = [
                    { orderId: { $regex: search, $options: 'i' } },
                    { waybill: { $regex: search, $options: 'i' } },
                ];
            }

            const orderCount = await Order.countDocuments(qry);

            const orders = await Order.find(qry)
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(limitNumber)
                .exec();

            const ordersWithStatusText = orders.map(order => ({
                ...order.toObject(), // Convert Mongoose document to plain object
                statusText: getStatusText(order.status),
                coupoun: order.coupoun
                    ? {
                          code: order.coupoun.code,
                          description: order.coupoun.description,
                          discountType: order.coupoun.discountType,
                          discountValue: order.coupoun.discountValue,
                          maxDiscount: order.coupoun.maxDiscount,
                          bundleDetails: order.coupoun.bundleDetails,
                          cDiscount: order.coupoun.cDiscount,
                      }
                    : null,
            }));

            const finalResponse = {
                items: ordersWithStatusText,
                count: orderCount,
                page: pageNumber,
                limit: limitNumber,
            };

            if (!orders || orders.length === 0) {
                return res.warn('error', req.__('NO_ORDERS_FOUND'));
            }

            return res.success(finalResponse, req.__('ORDERS_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.log('Error fetching orders:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async AllOrdersList(req, res) {
        const userId = req.user._id;
        const {
            page = 1,
            limit = 10,
            status,
            paymentMethod,
            minTotalPrice,
            maxTotalPrice,
            search,
            startDate,
            endDate,
        } = req.query;

        // Convert page and limit to numbers
        const pageNumber = parseInt(page);
        const limitNumber = parseInt(limit);

        // Calculate the number of items to skip
        let skip = (pageNumber - 1) * limitNumber;

        try {
            let qry = {};

            if (status) {
                const statusNumber = parseInt(status, 10);
                if (!isNaN(statusNumber) && Object.values(ShipRocketStatusCodes).includes(statusNumber)) {
                    qry.status = statusNumber;
                } else {
                    return res.badRequest({}, req.__('INVALID_ORDER_STATUS'));
                }
            }

            if (paymentMethod) {
                qry.paymentMethod = paymentMethod;
            }

            if (minTotalPrice && maxTotalPrice) {
                qry.totalPrice = { $gte: minTotalPrice, $lte: maxTotalPrice };
            } else if (minTotalPrice) {
                qry.totalPrice = { $gte: minTotalPrice };
            } else if (maxTotalPrice) {
                qry.totalPrice = { $lte: maxTotalPrice };
            }

            if (search) {
                qry.$or = [
                    { orderId: { $regex: search, $options: 'i' } },
                    { waybill: { $regex: search, $options: 'i' } },
                ];
            }

            // Date filtering
            if (startDate && endDate) {
                qry.createdAt = { $gte: new Date(startDate), $lte: new Date(endDate) };
            } else if (startDate) {
                qry.createdAt = { $gte: new Date(startDate) };
            } else if (endDate) {
                qry.createdAt = { $lte: new Date(endDate) };
            }

            const orderCount = await Order.countDocuments(qry);

            const orders = await Order.find(qry)
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(limitNumber)
                .populate('products.productId', 'name prices mainImage')
                .exec();

            const ordersWithStatusText = orders.map(order => ({
                ...order.toObject(), // Convert Mongoose document to plain object
                statusText: getStatusText(order.status),
            }));

            const finalResponse = {
                items: ordersWithStatusText,
                count: orderCount,
                page: pageNumber,
                limit: limitNumber,
            };

            if (!orders || orders.length === 0) {
                return res.warn([], req.__('NO_ORDERS_FOUND'));
            }

            return res.success(finalResponse, req.__('ORDERS_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            console.log('Error fetching orders:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async orderDetail(req, res) {
        const { orderId } = req.params;
        const userId = req.user._id;

        try {
            let qry = {};
            if (req.user.role === 'MASTER' || req.user.role === 'SUB_ADMIN') {
                qry = { _id: orderId };
            } else {
                qry = { _id: orderId, user: userId };
            }

            // Find the order by its ID and ensure it belongs to the user
            const order = await Order.findOne(qry)
                .populate('products.productId', 'name prices mainImage')
                .populate('shipments')
                .populate({
                    path: 'coupoun',
                    model: 'Coupon',
                    select: 'code description discountType discountValue maxDiscount bundleDetails',
                })
                .exec();

            // Check if the order exists
            if (!order) {
                return res.warn({}, req.__('ORDER_NOT_FOUND'));
            }

            // Filter unique shipments that have an AWB number
            const uniqueShipments = order.shipments.reduce((acc, shipment) => {
                if (shipment.awb_code && !acc.find(item => item.shipment_id === shipment.shipment_id)) {
                    acc.push(shipment);
                }
                return acc;
            }, []);

            if (uniqueShipments.length > 0) {
                order.shipments = uniqueShipments;
            }

            // Get the status text for the order
            const statusText = getStatusText(order.status);

            // Include coupon details in the response
            const couponDetails = order.coupoun
                ? {
                      code: order.coupoun.code,
                      description: order.coupoun.description,
                      discountType: order.coupoun.discountType,
                      discountValue: order.coupoun.discountValue,
                      maxDiscount: order.coupoun.maxDiscount,
                      bundleDetails: order.coupoun.bundleDetails,
                  }
                : null;

            // Return a success response with the order details
            const orderData = {
                ...order.toObject(),
                statusText,
                couponDetails,
            };

            return res.success(orderData, req.__('ORDER_FETCHED_SUCCESSFULLY'));
        } catch (error) {
            // Log the error for debugging purposes
            console.error('Error fetching order details:', error);
            // Return an internal server error response if an error occurs
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async trackOrder(req, res) {
        const { trackingId } = req.params;
        try {
            const status = await shipmentStatus(trackingId);
            res.success(status);
        } catch (error) {
            console.error('Error fetching order details:', error.message);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async cancelOrder(req, res) {
        const userId = req.user._id;
        const { orderId } = req.body;

        try {
            // Find the order
            const order = await Order.findOne({ _id: orderId, user: userId });

            if (!order) {
                return res.notFound({}, req.__('ORDER_NOT_FOUND'));
            }

            // Check if the order can be cancelled
            if (
                order.status === ShipRocketStatusCodes.CANCELED ||
                order.status === ShipRocketStatusCodes.CANCELED_BEFORE_SHIP
            ) {
                return res.badRequest({}, req.__('ORDER_ALREADY_CANCELLED'));
            }
            if (order.status === ShipRocketStatusCodes.DELIVERED) {
                return res.badRequest({}, req.__('ORDER_ALREADY_DELIVERED'));
            }

            // Update order status to 'Cancelled'
            order.status = ShipRocketStatusCodes.CANCELED_BEFORE_SHIP || ShipRocketStatusCodes.CANCELED;
            const cancelledOrder = await order.save();

            // Handle any additional logic for cancelling the order here
            // e.g., refund the payment if necessary

            return res.success(cancelledOrder, req.__('ORDER_CANCELLED'));
        } catch (error) {
            console.log('Error while cancelling order:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    // Method to process the refund
    async processRefund(req, res) {
        const { _id: userId } = req.user;
        const { orderId } = req.body;

        try {
            // Find the order
            const order = await Order.findOne({
                _id: orderId,
                // ...(roles === 'ADMIN' ? {} : { user: userId }),
            });
            console.log(order, 'order');
            if (!order) {
                return res.notFound({}, req.__('ORDER_NOT_FOUND'));
            }

            const razorpayPaymentId = order.paymentInfo.razorpay_payment_id;

            if (!razorpayPaymentId) {
                return res.badRequest({}, req.__('INVALID_PAYMENT_ID'));
            }
            console.log(order, 'order');
            const refundOptions = {
                paymentId: razorpayPaymentId,
                params: {
                    amount: order.totalPrice * 100, // Razorpay expects amount in paise
                    speed: 'normal',
                    receipt: shortid.generate(), // Optional receipt number
                },
            };

            console.log(refundOptions, 'refundOptions');
            try {
                // Perform the refund request using Razorpay's API in test mode
                const response = await razorpay.payments.refund(refundOptions.paymentId, refundOptions.params);

                // Log the response from Razorpay for debugging
                console.log('Refund response:', response);

                return res.success(response, req.__('REFUND_SUCCESS'));
            } catch (error) {
                console.log('Error while processing refund:', error);
                return res.serverError({ error }, req.__('INTERNAL_SERVER_ERROR'));
            }
        } catch (error) {
            console.log('Error while processing refund:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async updateOrderStatus(req, res) {
        const userId = req.user._id; // Assuming the user is authenticated
        let { orderId, status } = req.body;

        try {
            // Find the order by ID
            const order = await Order.findOne({ _id: orderId });

            if (!order) {
                return res.notFound({}, req.__('ORDER_NOT_FOUND'));
            }

            // Validate the new status using ShipRocketStatusCodes
            status = parseInt(status, 10);
            const validStatuses = Object.values(ShipRocketStatusCodes);
            if (!validStatuses.includes(status)) {
                return res.badRequest({}, req.__('INVALID_ORDER_STATUS'));
            }

            // Check if the order status is already the same
            if (order.status == status) {
                return res.badRequest({}, req.__('ORDER_STATUS_ALREADY_SET'));
            }

            // Prevent updating to 'Cancelled' if already delivered
            if (order.status === ShipRocketStatusCodes.DELIVERED && status === ShipRocketStatusCodes.CANCELED) {
                return res.badRequest({}, req.__('ORDER_ALREADY_DELIVERED_CANNOT_CANCEL'));
            }

            // Update the order status
            order.status = status;

            // If setting to 'Delivered', set the deliveredAt timestamp
            if (status === ShipRocketStatusCodes.DELIVERED) {
                order.deliveredAt = Date.now();
            }

            const updatedOrder = await order.save();

            // Additional logic for specific status updates could be added here
            // e.g., notifying the user, handling refunds, etc.
            if(status == 45){
                // Status 45 = CANCELLED_BEFORE_DISPATCHED
                // Call shiprocketCancelShipment internally
                try {
                    // Populate shipments to get Shipment IDs
                    const orderWithShipments = await Order.findById(orderId).populate('shipments');

                    if (orderWithShipments && orderWithShipments.shipments && orderWithShipments.shipments.length > 0) {
                        // Extract Shipment IDs from shipments
                        const shipmentIds = orderWithShipments.shipments
                            .filter(shipment => shipment.order_id)
                            .map(shipment => shipment.order_id);

                        if (shipmentIds.length > 0) {
                            console.log(`Attempting to cancel shipment for order ${orderId} with Shipment IDs:`, shipmentIds);

                            // Shiprocket API endpoint for cancelling shipments
                            const endpoint = `/orders/cancel`;
                            const shipmentData = {
                                "ids": shipmentIds
                            };

                            // Make the API request using makeApiRequest (handles token internally)
                            const cancelData = await makeApiRequest(endpoint, 'POST', shipmentData);

                            if (cancelData) {
                                console.log('Shipment cancelled successfully:', cancelData);
                            } else {
                                console.warn('Failed to cancel shipment with Shiprocket - no response data');
                            }
                        } else {
                            console.log('No Shipment IDs found for order:', orderId);
                        }
                    } else {
                        console.log('No shipments found for order:', orderId);
                    }
                } catch (shipmentError) {
                    console.error('Error during shipment cancellation:', shipmentError);
                    // Continue with order status update even if shipment cancellation fails
                }
            }

            return res.success(updatedOrder, req.__('ORDER_STATUS_UPDATED'));
        } catch (error) {
            console.error('Error while updating order status:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
}
module.exports = new OrderController();
